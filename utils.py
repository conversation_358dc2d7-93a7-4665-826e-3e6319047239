import io
from pathlib import Path

import cv2
import numpy as np
import requests
from PIL import Image
from requests.exceptions import Timeout, ProxyError, ConnectionError, SSLError, RequestException

from delta_image import background_removal

def numpy_image_to_bytes(numpy_image):


    # 转换为 BytesIO 对象
    result, encoded_image = cv2.imencode('.jpg', numpy_image)
    # 将编码后的图像转换为字节流
    return encoded_image.tobytes()

def image_to_cv2(img_path):
    # 使用Pillow打开图像
    pil_image = Image.open(img_path)

    # 将Pillow图像转换成NumPy数组
    image_array = np.array(pil_image)

    # Pillow的数组是RGB顺序，转换成OpenCV的BGR顺序
    image_array_bgr = cv2.cvtColor(image_array, cv2.COLOR_RGB2BGR)
    return image_array_bgr

def bytes_to_cv2(image_bytes):
    nparr = np.frombuffer(image_bytes, np.uint8)

    # 使用 cv2.imdecode 解码为 OpenCV 图像
    return cv2.imdecode(nparr, cv2.IMREAD_COLOR)


def request_with_retry(
        url,
        method='GET',
        params=None,
        allow_redirects=True,
        headers=None,
        cookies=None,
        proxy=None,
        timeout=5,
        json=None,
        data=None,
        max_retries=3
):
    """
    带自动重试功能的请求函数

    参数：
    url: 请求地址
    method: HTTP方法（默认GET）
    headers: 请求头（默认None）
    cookies: cookies（默认None）
    proxy: 代理地址（默认None）
    timeout: 超时时间秒数（默认5）
    max_retries: 最大重试次数（默认3）

    返回：
    requests.Response对象

    异常：
    重试失败后抛出最后捕获的异常
    """
    headers = headers or {}
    cookies = cookies or {}
    proxies = proxy if proxy else {}
    method = method.upper()

    for attempt in range(max_retries + 1):
        try:
            response = requests.request(
                method=method,
                url=url,
                params=params,
                headers=headers,
                cookies=cookies,
                proxies=proxies,
                json=json,
                data=data,
                allow_redirects=allow_redirects,
                timeout=timeout
            )
            return response
        except (Timeout, ProxyError, ConnectionError, SSLError) as e:
            if attempt < max_retries:
                continue
            raise RequestException(
                f"Request failed after {max_retries} retries") from e
        except RequestException as e:
            raise RequestException(
                f"Request error occurred: {str(e)}") from e

    raise RequestException(f"Request failed after {max_retries} retries")


def bytes_image_to_cv2(image_bytes):
    nparr = np.frombuffer(image_bytes, np.uint8)
    # 使用 cv2.imdecode 解码为 OpenCV 图像
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)  # 或 cv2.IMREAD_GRAYSCALE 等模式
    return img


def read_all_background():
    return [image_to_cv2(entry) for entry in OUT_IMAGE_ENTRIES]


def image_contrast_std(image, background_image):
    return np.std(image - background_image)


def find_background(image, background_list):
    min_std_score = float('inf')
    min_background = None
    for background in background_list:
        std_score = image_contrast_std(image, background)
        # 更新最小分数和对应的背景
        if std_score < min_std_score:
            min_std_score = std_score
            min_background = background
    return min_background


def process_clear_background(image, background_list, write_background=True):
    background = find_background(image, background_list)
    background_removal_image = background_removal(image, background)
    if write_background:
        return 255 - background_removal_image
    return background_removal_image
def process_clear(image,background):
    image = bytes_to_cv2(image)
    background = bytes_to_cv2(background)
    # show(image)
    # show(background)

    background_removal_image = background_removal(image, background)

    return 255 - background_removal_image

def show(img, name="logo"):
    cv2.imshow(name, img)
    cv2.waitKey(0)
    cv2.destroyAllWindows()

def show_bytes_image(bytes):
    image_stream = io.BytesIO(bytes)

    # 用 Pillow 打开图像
    _image = Image.open(image_stream)

    # 显示图像（使用系统默认的图片查看器）
    _image.show()

OUT_IMAGE_DIR = "out_img"
out_directory_path = Path(OUT_IMAGE_DIR)
OUT_IMAGE_ENTRIES = out_directory_path.iterdir()
