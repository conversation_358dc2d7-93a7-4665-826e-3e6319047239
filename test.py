import requests

cookies = {
    '__jsluid_s': 'c63746ee910f6d48803264fe14b4ef33',
}

headers = {
    'Accept': 'application/json, text/plain, */*',
    'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Content-Type': 'application/json',
    'Origin': 'https://beian.miit.gov.cn',
    'Pragma': 'no-cache',
    'Referer': 'https://beian.miit.gov.cn/',
    'Sec-Fetch-Dest': 'empty',
    'Sec-Fetch-Mode': 'cors',
    'Sec-Fetch-Site': 'same-site',
    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/140.0.0.0 Safari/537.36',
    'sec-ch-ua': '"Chromium";v="140", "Not=A?Brand";v="24", "Google Chrome";v="140"',
    'sec-ch-ua-mobile': '?0',
    'sec-ch-ua-platform': '"macOS"',
    'token': 'eyJ0eXBlIjoxLCJ1IjoiMDk4ZjZiY2Q0NjIxZDM3M2NhZGU0ZTgzMjYyN2I0ZjYiLCJzIjoxNzU5MTUwNDYzMjk3LCJlIjoxNzU5MTUwOTQzMjk3fQ.fjUybsztNpZZsJHjU1bqKMGT9kyGjjm-foEm5AftLPs',
    # 'Cookie': '__jsluid_s=c63746ee910f6d48803264fe14b4ef33',
}

json_data = {
    'clientUid': 'point-b734ed98-112c-4c45-8715-b3fd91911c43',
}

response = requests.post(
    'https://hlwicpfwc.miit.gov.cn/icpproject_query/api/image/getCheckImagePoint',
    cookies=cookies,
    headers=headers,
    json=json_data,
)

print(response.text)