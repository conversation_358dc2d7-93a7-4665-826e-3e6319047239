import base64
import hashlib
import json
import random
import time
import traceback
from collections import namedtuple
from pathlib import Path
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from redis_client import redis_server
from error import CaptchaError
from utils import request_with_retry, image_to_cv2
from loguru import logger
from ocr import Ocr

# 添加日志记录器，按天切割日志文件
logger.add(
    "logs/app_{time:YYYY-MM-DD}.log",  # 日志文件路径和命名格式
    rotation="00:00",  # 每天凌晨 0 点切割
    retention="7 days",  # 保留 7 天日志，可根据需求设置
    compression="zip",  # 压缩旧日志文件（可选）
    encoding="utf-8",  # 日志文件编码
    level="INFO"  # 设置日志等级
)

CaptchaConfig = namedtuple(
    'CaptchaConfig', ["original_image_base64", "secret_key", "token", "word_list"])


class CaptchaByPass:
    ocr = Ocr()
    background_list = [image_to_cv2(entry) for entry in Path('out_img').iterdir()]
    proxys = ['*************', '*************', '************', '************', '***************', '*************',
              '************', '************', '************', '*************', '**************', '**************',
              '*************', '************', '*************', '************', '**************', '***********',
              '************', '*************', '*************', '************', '*************', '************',
              '*************', '***********', '************', '*************', '***********', '*************',
              '*************', '*************', '************', '47.96.160.129', '47.98.131.37', '47.96.163.166',
              '47.98.133.199', '47.98.109.34', '118.31.79.152', '47.98.131.215', '47.97.190.87', '47.98.149.212',
              '47.96.14.168', '47.96.22.138', '47.97.200.5', '47.97.152.183', '116.62.117.147', '118.31.39.206',
              '120.27.209.52', '47.98.98.251', '47.97.183.118', '47.96.184.195', '47.98.135.25', '116.62.230.181',
              '47.98.45.104', '47.96.12.40', '47.97.117.179', '47.97.212.132', '47.98.122.230', '47.98.143.169',
              '47.98.47.173', '47.97.230.134', '47.97.207.217', '47.98.96.171', '47.97.152.46', '47.97.179.195',
              '47.96.11.199', '116.62.190.191', '47.97.180.223', '47.98.43.120', '47.98.119.138', '47.97.198.137',
              '47.97.173.53', '116.62.138.189', '121.196.194.130', '47.97.229.129', '120.55.61.191', '118.31.48.60',
              '120.55.52.117', '47.97.187.68', '118.31.2.117', '121.43.169.243', '47.97.183.120', '47.97.184.30',
              '47.96.182.199', '47.97.121.140', '118.31.59.116', '47.97.113.81', '116.62.187.1', '120.27.226.13',
              '120.27.218.251', '47.98.131.107', '47.97.214.106', '118.31.4.148', '47.98.102.67', '114.55.124.104',
              '47.96.187.173', '118.31.52.38', '47.96.177.21', '47.96.169.49', '47.97.106.89', '47.97.105.238',
              '47.96.141.71', '47.97.192.149', '116.62.114.23', '47.98.48.178', '47.97.173.98', '47.97.228.40',
              '118.31.76.0', '47.98.103.80', '47.96.179.94', '47.96.135.75', '47.97.163.43', '116.62.237.80',
              '47.97.96.254', '116.62.175.17', '47.97.172.4', '47.97.215.188', '47.97.111.176', '101.37.148.64',
              '47.97.110.149', '47.98.52.109', '47.98.61.60', '47.96.175.179', '47.96.4.210', '118.31.19.10',
              '116.62.156.123', '47.97.110.253', '120.55.46.137', '47.96.167.13', '118.31.62.208', '120.27.217.160',
              '116.62.64.129', '47.98.59.246', '47.96.30.89', '47.96.9.44', '47.97.190.30', '47.97.97.187',
              '118.31.40.132', '47.98.51.128', '120.27.230.101', '47.97.160.165', '47.97.212.161', '47.98.129.98',
              '47.96.18.20', '47.98.59.153', '47.97.218.62', '47.97.127.195', '120.27.230.252', '121.43.189.167',
              '118.31.63.48', '47.97.190.101', '118.31.20.42', '47.96.31.197', '47.97.120.224', '47.97.159.73',
              '101.37.124.27', '116.62.245.153', '47.98.62.136', '118.31.23.232', '121.196.222.164', '120.27.211.153',
              '47.96.133.5', '121.43.179.83', '47.97.172.128', '118.31.66.165', '47.96.141.80', '47.97.177.186',
              '118.31.69.136', '118.31.76.52', '120.27.219.41', '47.96.9.29', '116.62.145.233', '121.43.186.161',
              '120.55.55.188', '121.43.183.138', '116.62.200.179', '47.97.122.107', '47.98.37.77', '47.96.143.224',
              '47.98.38.199', '47.97.209.179', '116.62.119.120', '47.96.159.87', '47.97.222.200', '47.97.118.111',
              '47.97.98.8', '47.98.57.199', '116.62.240.240', '47.97.179.192', '118.31.35.239', '101.37.148.131',
              '118.31.61.151', '47.97.168.192', '47.97.184.196', '116.62.143.2', '120.55.47.206', '121.196.218.182',
              '47.98.38.78', '47.97.116.219', '47.98.40.76', '116.62.209.150', '121.43.173.179', '47.98.43.84',
              '47.97.216.161', '47.96.145.7', '120.27.220.136', '116.62.22.2', '47.96.165.16', '116.62.197.19',
              '47.97.197.27', '47.96.28.6', '116.62.204.137', '118.31.20.188', '116.62.137.77', '47.96.138.89',
              '47.97.208.123', '47.96.177.160', '116.62.115.244', '47.96.140.4', '47.97.180.30', '47.97.168.214',
              '118.31.62.72', '47.97.185.232', '120.27.225.84', '47.97.167.168', '116.62.56.66', '47.96.173.108',
              '47.97.217.246', '121.43.169.185', '47.97.166.106', '47.96.13.55', '116.62.124.27', '114.55.125.80',
              '47.97.166.157', '116.62.63.162', '47.97.113.27', '116.62.127.129', '47.97.166.100', '47.97.110.32',
              '121.43.181.36', '116.62.160.179', '116.62.198.94', '47.96.28.255', '121.196.219.55', '47.96.190.1',
              '120.55.60.4', '116.62.126.62', '116.62.148.57', '120.27.212.221', '47.97.98.214', '121.43.185.46',
              '116.62.71.170', '116.62.205.11', '47.96.186.65', '118.31.2.121', '120.55.63.190', '47.97.195.127',
              '118.31.10.239', '47.97.108.226', '120.27.228.217', '101.37.148.63', '47.96.157.43', '47.96.5.92',
              '116.62.186.181', '47.97.207.41', '47.97.218.23', '47.97.125.142', '47.97.174.233', '118.31.75.31',
              '120.55.61.149', '116.62.186.141', '118.31.56.117', '47.97.191.28', '47.97.123.143', '47.97.185.137',
              '120.55.57.183', '47.97.161.50', '116.62.202.148', '118.31.77.94', '47.96.19.137', '118.31.65.136',
              '47.96.159.204', '114.55.126.59', '47.96.176.116', '120.55.40.164', '121.196.217.162', '116.62.241.36',
              '47.97.161.217', '47.97.169.68', '120.55.54.51', '47.97.97.57', '116.62.20.105', '47.97.193.25',
              '116.62.171.8', '101.37.151.142', '47.97.179.250', '47.96.163.105', '47.97.178.86', '116.62.58.214',
              '120.55.44.8', '47.96.163.233', '47.97.125.239', '116.62.198.209', '47.96.150.231', '116.62.226.65',
              '47.97.163.88', '47.97.171.195', '116.62.67.249', '120.27.231.195', '120.55.60.111', '47.96.14.115',
              '120.27.223.149', '47.96.188.90', '47.96.158.100', '47.96.14.218', '47.96.185.147', '121.43.182.158',
              '47.97.96.111', '47.96.141.144', '118.31.17.0', '47.97.103.186', '47.97.174.85', '121.43.173.113',
              '116.62.123.97', '116.62.243.200', '101.37.152.199', '116.62.230.29', '121.43.173.234', '47.97.164.173',
              '116.62.62.197', '121.43.189.197', '120.27.229.44', '118.31.14.153', '47.97.176.188', '118.31.62.97',
              '121.196.202.105', '116.62.112.43', '47.97.119.205', '47.97.119.215', '47.96.8.243', '47.97.163.39',
              '116.62.206.23', '47.97.122.226', '47.96.177.138', '47.97.165.44', '47.97.122.171', '121.43.165.196',
              '47.96.13.48', '118.31.2.172', '47.97.162.211', '116.62.243.32', '116.62.122.9', '116.62.63.217',
              '116.62.246.124', '116.62.189.127', '116.62.143.114', '116.62.19.78', '121.43.175.25', '116.62.166.26',
              '121.196.209.101', '116.62.153.40', '120.55.46.247', '121.196.208.227', '47.97.162.75', '116.62.18.59',
              '118.31.20.247', '116.62.186.240', '121.196.203.91', '116.62.186.147', '116.62.231.190', '116.62.222.61',
              '116.62.229.122', '101.37.146.202', '118.31.72.28', '120.27.217.131', '121.43.180.233', '118.31.77.158',
              '120.55.62.251', '47.96.6.105', '47.96.180.151', '116.62.153.79', '116.62.202.124', '121.196.198.122',
              '116.62.172.146', '118.31.61.17', '47.96.7.88', '116.62.150.219', '116.62.113.118', '47.96.0.237',
              '121.43.168.31', '47.96.180.153', '47.96.181.168', '116.62.146.207', '118.31.63.175', '101.37.152.201',
              '121.196.220.180', '116.62.18.180', '47.96.174.192', '47.97.182.29', '47.98.139.215', '47.97.220.166',
              '47.98.159.145', '121.43.169.163', '47.98.48.36', '47.98.195.5', '47.98.46.117', '47.98.199.160',
              '47.97.219.29', '47.97.193.26', '116.62.167.75', '116.62.117.79', '47.97.253.254', '47.98.200.255',
              '47.96.13.141', '47.97.160.126', '121.43.162.238', '120.55.43.59', '47.98.192.244', '121.40.132.214']
    proxy_auth = "juming:juming123456..."
    proxy_prot = '32188'

    def init_captcha(self, proxy):
        timestamp = int(time.time() * 1000)
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json; charset=UTF-8',
            'Origin': 'https://beian.mps.gov.cn',
            'Pragma': 'no-cache',
            'Referer': 'https://beian.mps.gov.cn/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'timestamp': str(timestamp),
        }

        json_data = {
            'captchaType': 'clickWord',
            'clientUid': None,
            'ts': timestamp,
        }

        response = request_with_retry('https://beian.mps.gov.cn/cyber_portal/captcha/get', method="post",
                                      headers=headers,
                                      json=json_data, proxy=proxy)
        try:
            response_json = response.json()
            rep_data = response_json.get("repData")
            if rep_data is None:
                raise ValueError(f"请求失败:response:{response.text}")

            return CaptchaConfig(
                original_image_base64=rep_data.get('originalImageBase64'),
                secret_key=rep_data.get("secretKey"),
                token=rep_data.get("token"),
                word_list=rep_data.get("wordList"),
            )


        except (json.decoder.JSONDecodeError, ValueError) as e:
            logger.exception(f"{e}\n{response.text}")
            return None

    def aes_encrypt(self, plaintext: str, key: str) -> str:
        """
        AES加密（ECB模式，PKCS7填充）
        :param plaintext: 明文（字符串）
        :param key: 密钥（任意长度字符串，会被哈希为32字节）
        :return: Base64编码的加密结果
        """
        # 使用 SHA256 对 key 进行哈希，确保 32 字节长度

        # 创建加密器（ECB模式）
        cipher = AES.new(key.encode(), AES.MODE_ECB)
        # 加密并填充
        ciphertext = cipher.encrypt(pad(plaintext.encode(), AES.block_size))

        # 返回 Base64 编码的密文
        return base64.b64encode(ciphertext).decode("utf-8")

    def image_recognition(self, base64_str):
        if base64_str is None:
            raise Exception('Image Base64 str is None')
        # 解码为字节数据
        image_data = base64.b64decode(base64_str)
        # image_cv2 = bytes_image_to_cv2(image_data)
        # result = process_clear_background(image_cv2,self.background_list)
        # show(result)
        # # 转换为 BytesIO 对象
        # result, encoded_image = cv2.imencode('.jpg', result)
        # # 将编码后的图像转换为字节流
        # return self.ocr.text_point(encoded_image.tobytes())
        image_data = base64.b64decode(base64_str)
        return self.ocr.text_point(image_data)

    def match_word(self, word_list, word_points):
        match_words_points = []
        if not all(i in word_points for i in word_list):
            raise Exception(f"word 识别失败,word_list:{word_list},word_points:{word_points}")
        for word in word_list:
            if word_points.get(word) is None:
                continue
            point = word_points.get(word)
            match_words_points.append({
                "x": int((point[0] + point[2]) / 2 / 0.9885057471264368),
                "y": int((point[1] + point[3]) / 2 / 0.9885057471264368)
            })
        return json.dumps(match_words_points, separators=(",", ":"))

    def check(self, point_text, secret_key, token, proxy):

        timestamp = int(time.time() * 1000)
        point_json = self.aes_encrypt(point_text, secret_key)
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json; charset=UTF-8',
            'Origin': 'https://beian.mps.gov.cn',
            'Pragma': 'no-cache',
            'Referer': 'https://beian.mps.gov.cn/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'X-Requested-With': 'XMLHttpRequest',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'timestamp': str(timestamp),
        }

        json_data = {
            'captchaType': 'clickWord',
            'pointJson': point_json,
            'token': token,
        }

        response = request_with_retry('https://beian.mps.gov.cn/cyber_portal/captcha/check', method="post",
                                      headers=headers, json=json_data, proxy=proxy)
        try:
            return response.json()
        except (json.decoder.JSONDecodeError, ValueError) as e:
            logger.exception(f"{e}\n{response.text}")
            return None

    def web_search(self, point_text, secret_key, token, query_type, query_value, proxy):

        timestamp = int(time.time() * 1000)
        captcha_verification = self.aes_encrypt(f'{token}---{point_text}', secret_key)
        logger.info(f'{token}---{point_text}---{secret_key}')
        headers = {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Cache-Control': 'no-cache',
            'Connection': 'keep-alive',
            'Content-Type': 'application/json',
            'Origin': 'https://beian.mps.gov.cn',
            'Pragma': 'no-cache',
            'Referer': 'https://beian.mps.gov.cn/',
            'Sec-Fetch-Dest': 'empty',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Site': 'same-origin',
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"macOS"',
            'timestamp': str(timestamp),
        }

        json_data = {
            query_type: query_value,
            'captchaVerification': captcha_verification,
        }

        response = request_with_retry('https://beian.mps.gov.cn/cyber_portal/query/websearch', method="post",
                                      headers=headers, json=json_data, proxy=proxy)
        try:
            return response.json()
        except (json.decoder.JSONDecodeError, ValueError) as e:
            logger.exception(f"{e}\n{response.text}")
            return None

    def query(self, query_type="maindm", query_value=""):
        logger.info(f"{query_value}开始查询")
        proxy = random.choice(self.proxys)
        proxies = {
            "https": f"http://{self.proxy_auth}@{proxy}:{self.proxy_prot}",
            "http": f"http://{self.proxy_auth}@{proxy}:{self.proxy_prot}"
        }
        init_captcha_config = self.init_captcha(proxies)
        logger.info("[*]初始化验证码配置成功")
        text_points = self.image_recognition(init_captcha_config.original_image_base64)
        match_words_points = self.match_word(init_captcha_config.word_list, text_points)
        logger.info(f"[*]匹配文字坐标:{match_words_points}")
        check_result = self.check(match_words_points, init_captcha_config.secret_key, init_captcha_config.token, proxies)
        if not check_result.get("repData", {}).get("result"):
            raise CaptchaError("验证失败")
        result = self.web_search(match_words_points, init_captcha_config.secret_key, init_captcha_config.token,
                                 query_type, query_value,
                                 proxies)
        if not result.get("success"):
            raise CaptchaError(result.get("data"))
        result["proxy"] = proxies
        logger.success(f"[*]{query_value}查询成功!")
        return result

    def submit(self):
        for _ in range(3):
            proxy = random.choice(self.proxys)
            proxies = {
                "https": f"http://{self.proxy_auth}@{proxy}:{self.proxy_prot}",
                "http": f"http://{self.proxy_auth}@{proxy}:{self.proxy_prot}"
            }
            try:
                init_captcha_config = self.init_captcha(proxies)
                logger.info("[*]初始化验证码配置成功")
                text_points = self.image_recognition(init_captcha_config.original_image_base64)
                match_words_points = self.match_word(init_captcha_config.word_list, text_points)
                logger.info(f"[*]匹配文字坐标:{match_words_points}")
                check_result = self.check(match_words_points, init_captcha_config.secret_key, init_captcha_config.token,
                                          proxies)
                if not check_result.get("repData", {}).get("result"):
                    continue
                logger.info(f"{match_words_points},{init_captcha_config.secret_key},{init_captcha_config.token}")
                return {
                    "match_words_points": match_words_points,
                    "secret_key": init_captcha_config.secret_key,
                    "token": init_captcha_config.token,
                    "proxies":proxies
                }
            except Exception as e:
                logger.exception(e)


    def query_domain(self, data, query_type="maindm", query_value=""):
        proxies = data.get("proxies", {})
        match_words_points = data.get("match_words_points")
        secret_key = data.get("secret_key")
        token = data.get("token")

        result = self.web_search(match_words_points, secret_key, token,
                                 query_type, query_value,
                                 proxies)
        if not result.get("success") and result.get("data") == "验证失败":
            raise CaptchaError(f"{query_value} {result} {proxies}")
        result["proxy"] = proxies
        logger.success(f"[*]{query_value}查询成功!")
        return result

    def test_ocr(self):
        start = time.time()
        data = "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"
        word_list = [
            "级",
            "合",
            "名"
        ]
        text_points = self.image_recognition(data)
        match_words_points = self.match_word(word_list, text_points)
        logger.info(match_words_points)
        print(f"end:{time.time() - start}")


if __name__ == '__main__':
    captcha_pass = CaptchaByPass()
    count = 0
    for _ in range(100):
        try:
            start = time.time()
            print(captcha_pass.query(query_value="baidu.com"))
            print(f"end:{time.time() - start}")
            count += 1
        except Exception as e:
            logger.error(e)
    print(count/100)
    # print(captcha_pass.aes_encrypt('[{"x":86,"y":83},{"x":179,"y":20},{"x":236,"y":86}]','JCy0ZOWg87Ot3Vmz'))
    # for i in range(1, 20):
    #     try:
    #         captcha_pass.test_ocr()
    #         result = captcha_pass.submit()
    #         sleep_time = 1
    #         time.sleep(sleep_time)
    #         print(f"睡眠时间:{sleep_time}")
    #         captcha_pass.query_domain(data=result,
    #                                   query_type="maindm", query_value="baidu.com")
    #     except Exception:
    #         print("验证失败")
