import base64
import json
import time
import uuid

import requests
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad

from utils import request_with_retry
from ocr.ocr import Ocr


ocr = Ocr()

def md5_hex(message):
    import hashlib
    md5 = hashlib.md5()
    md5.update(message.encode('utf-8'))
    return md5.hexdigest()

def get_token():
    cookies = {
        '__jsluid_s': '9f9074939125698f6001e55d84f8bde6',
    }

    url = 'https://hlwicpfwc.miit.gov.cn/icpproject_query/api/auth'
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://beian.miit.gov.cn/",
        "Content-Type": "application/x-www-form-urlencoded",
        "Connection": "keep-alive",
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Origin": "https://beian.miit.gov.cn"
    }

    time_stamp = int(time.time())
    auth_key = md5_hex(f"testtest{time_stamp}")
    data= {
        'authKey': auth_key,
        'timeStamp': time_stamp
    }
    response = request_with_retry(url, method="post", headers=headers, data=data)
    response.raise_for_status()
    json_data = response.json()
    params = json_data.get("params")
    return params.get("bussiness")

def get_check_image_point(token):
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://beian.miit.gov.cn/",
        "Token": token,
        "Connection": "keep-alive",
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Origin": "https://beian.miit.gov.cn"
    }

    data = {
        'clientUid': f'point-{uuid.uuid4()}',
    }

    response = request_with_retry(
        'https://hlwicpfwc.miit.gov.cn/icpproject_query/api/image/getCheckImagePoint',
        method="POST",
        headers=headers,
        json=data,
    )
    response.raise_for_status()
    json_data = response.json()
    params = json_data.get("params",{})
    params["clientUid"] = data.get("clientUid")
    return params

def aes_encrypt(plaintext: str, key: str) -> str:
    """
    AES加密（ECB模式，PKCS7填充）
    :param plaintext: 明文（字符串）
    :param key: 密钥（任意长度字符串，会被哈希为32字节）
    :return: Base64编码的加密结果
    """
    # 使用 SHA256 对 key 进行哈希，确保 32 字节长度
    # 创建加密器（ECB模式）
    cipher = AES.new(key.encode(), AES.MODE_ECB)
    # 加密并填充
    ciphertext = cipher.encrypt(pad(plaintext.encode(), AES.block_size))
    # 返回 Base64 编码的密文
    return base64.b64encode(ciphertext).decode("utf-8")


def generate_point_json(big_img, small_img, secret_key):
    """生成点击坐标JSON"""
    points = ocr.ocr(big_img, small_img)
    new_points = [[p[0] + 20, p[1] + 20] for p in points]
    point_json = [{"x": p[0], "y": p[1]} for p in new_points]
    return aes_encrypt(json.dumps(point_json).replace(" ", ""), secret_key)


def checkImage(token, uuid_token, secretKey, clientUid, pointJson):
    """验证图片点击"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://beian.miit.gov.cn/",
        "Token": token,
        "Connection": "keep-alive",
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Origin": "https://beian.miit.gov.cn"
    }
    data = {
        "token": uuid_token,
        "secretKey": secretKey,
        "clientUid": clientUid,
        "pointJson": pointJson
    }
    print(data)
    print(token)
    response = request_with_retry("https://hlwicpfwc.miit.gov.cn/icpproject_query/api/image/checkImage",method="POST",
                        headers=headers, json=data)

    response.raise_for_status()
    json_data = response.json()
    print(json_data)
    return json_data.get("params")


def query_domain(token, sign, uuid_token, domain):

    """查询域名ICP信息"""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "Referer": "https://beian.miit.gov.cn/",
        "Token": token,
        "Sign": sign,
        "Uuid": uuid_token,
        "Connection": "keep-alive",
        "Accept": "application/json, text/plain, */*",
        "Accept-Encoding": "gzip, deflate, br",
        "Accept-Language": "zh-CN,zh;q=0.9",
        "Origin": "https://beian.miit.gov.cn",
        "Content-Type": "application/json",
        "Cookie": "__jsluid_s=" + str(uuid.uuid4().hex[:32])
    }
    print(headers)
    data = {"pageNum": "", "pageSize": "", "unitName": domain, "serviceType": 1}

    response = requests.post(
        'https://hlwicpfwc.miit.gov.cn/icpproject_query/api/icpAbbreviateInfo/queryByCondition',
        headers=headers,
        data=json.dumps(data, ensure_ascii=False, separators=(",", ":")),
    )
    print(response.text)
    json_data = response.json()
    return json_data.get("params")

def run():
    token = get_token()
    image_point = get_check_image_point(token)
    big_img = base64.b64decode(image_point.get("bigImage"))
    small_img = base64.b64decode(image_point.get("smallImage"))
    secret_key = image_point.get("secretKey")
    client_uid = image_point.get("clientUid")
    uuid_token = image_point.get("uuid")
    point_json = generate_point_json(big_img,small_img,secret_key)
    result = checkImage(token, uuid_token, secret_key, client_uid, point_json)
    sign = result.get("sign")
    if sign is None:
        raise Exception("验证失败")
    print(sign)
    result = query_domain(token, sign, uuid_token, "baidu.com")
    return result

if __name__ == '__main__':

    run()