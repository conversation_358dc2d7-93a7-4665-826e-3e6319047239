import multiprocessing
import signal
from typing import Any, Dict, Optional
from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status, Query
from fastapi.responses import JSONResponse
from fastapi.encoders import jsonable_encoder
from loguru import logger
from pydantic import BaseModel

from captcha_bypass import Captcha<PERSON>yPass
from captcha_pool import <PERSON><PERSON><PERSON><PERSON>
from config import config
import uvicorn
from redis_client import redis_server

captcha_pass = CaptchaByPass()
cc_pool = CaptchaPool()

# ----------------- 统一响应模型定义 -----------------
class BaseResponse(BaseModel):
    code: int
    message: str
    data: Optional[Any] = None


class SuccessResponse(BaseResponse):
    def __init__(self, data: Any = None, message: str = "success"):
        super().__init__(code=200, message=message, data=data)


class ErrorResponse(BaseResponse):
    def __init__(self, code: int, message: str):
        super().__init__(code=code, message=message)


# ----------------- 异常处理器 -----------------
async def http_exception_handler(request, exc: HTTPException):
    return JSONResponse(
        status_code=exc.status_code,
        content=jsonable_encoder(
            ErrorResponse(
                code=exc.status_code,
                message=exc.detail
            )
        )
    )


async def general_exception_handler(request, exc: Exception):
    return JSONResponse(
        status_code=500,
        content=jsonable_encoder(
            ErrorResponse(
                code=500,
                message="查询失败"
            )
        )
    )


app = FastAPI(name=config.app.name)

# 注册异常处理器
app.add_exception_handler(HTTPException, http_exception_handler)
app.add_exception_handler(Exception, general_exception_handler)

background_process = None
@app.on_event("startup")
async def startup_event():
    global background_process
    print("FastAPI 应用启动")
    # 创建并启动后台进程
    background_process = multiprocessing.Process(target=cc_pool.start_producer)
    background_process.daemon = True  # 设置为守护进程，主进程结束时自动终止
    background_process.start()
    print(f"后台进程已启动，PID: {background_process.pid}")


@app.on_event("shutdown")
async def shutdown_event():
    global background_process
    print("FastAPI 应用关闭")

    # 终止后台进程
    if background_process and background_process.is_alive():
        background_process.terminate()
        background_process.join()  # 等待进程结束
        print("后台进程已终止")


# 注册信号处理，确保进程在异常情况下也能被清理
def signal_handler(signum, frame):
    global background_process
    if background_process and background_process.is_alive():
        background_process.terminate()
        background_process.join()
    exit(0)


signal.signal(signal.SIGTERM, signal_handler)
signal.signal(signal.SIGINT, signal_handler)  # 处理 Ctrl+C


@app.get("/beian/search", response_model=BaseResponse)
def beian_search(keywords: str):
    if keywords is None:
        raise ValueError("请输入有效的请求参数")
    exception = None
    for _ in range(10):
        try:
            data = redis_server.get_and_delete_random_key()
            if data is None:
                data = captcha_pass.submit()
            result = captcha_pass.query_domain(data=data, query_value=keywords)
            return SuccessResponse(data=result)
        except Exception as e:
            exception = e
            logger.error(f"查找失败:{e}")
    raise exception


@app.get("/health", response_model=BaseResponse)
def get_health():
    """健康检查"""
    try:
        # 添加更多健康检查逻辑
        return SuccessResponse(data={"status": "healthy"})
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_503_SERVICE_UNAVAILABLE,
            detail="Service unavailable"
        )


if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=config.app.port)
