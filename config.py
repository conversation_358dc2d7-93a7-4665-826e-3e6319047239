# config.py
import os
from typing import Any, Dict
import yaml

class ConfigError(Exception):
    """自定义配置异常"""
    pass

class Config:
    _instance = None
    _config: Dict[str, Any] = None
    _loaded = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super(Config, cls).__new__(cls)
        return cls._instance

    def __init__(self):
        if not self._loaded:
            self.load_config()
            self._loaded = True

    def load_config(self, config_file: str = None):
        """
        加载YAML配置文件
        :param config_file: 配置文件路径，默认使用项目根目录下的config.yml
        """
        if not config_file:
            config_file = os.path.join(
                os.path.dirname(os.path.abspath(__file__)),
                'config.yml'
            )

        try:
            with open(config_file, 'r') as f:
                config_data = yaml.safe_load(f)
        except FileNotFoundError:
            raise ConfigError(f"配置文件未找到: {config_file}")
        except yaml.YAMLError as e:
            raise ConfigError(f"配置文件解析失败: {str(e)}")
        except Exception as e:
            raise ConfigError(f"加载配置失败: {str(e)}")

        # 将字典转换为对象属性
        self._config = self._dict_to_object(config_data)

    def _dict_to_object(self, d: Dict[str, Any]) -> Any:
        """递归将字典转换为对象"""
        if isinstance(d, dict):
            return type('ConfigDict', (), {k: self._dict_to_object(v) for k, v in d.items()})
        elif isinstance(d, list):
            return [self._dict_to_object(v) for v in d]
        else:
            return d

    def __getattr__(self, name: str) -> Any:
        """通过属性访问配置"""
        return getattr(self._config, name, None)

# 创建全局配置实例
config = Config()
