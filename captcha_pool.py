import copy
import json
import threading
import time
import traceback
from collections import deque
from concurrent.futures import Thread<PERSON>oolExecutor
from loguru import logger
from captcha_bypass import CaptchaByPass
from config import config
from redis_client import redis_server


class CaptchaPool:
    executor = ThreadPoolExecutor(max_workers=5)
    pool = deque()
    pool_lock = threading.Lock()
    captcha = CaptchaByPass()

    def remove_done_task(self, future):
        """任务完成回调"""
        try:
            result = future.result()
            logger.info(f"[*]:result:{result}")
            if result is None:
                return
            if result:
                # 将生成的Cookie添加到对应队列
                redis_server.push_queue(result)
                logger.info(f"{result}推送成功")
        except Exception as e:
            traceback.print_exc()
            logger.error(f"任务执行失败: {str(e)}")
        finally:
            self.pool.remove(future)




    def start_producer(self):
        while True:
            if redis_server.safe_zadd_with_size_limit(config.spider.redis_key, config.spider.redis_pool_len):
                time.sleep(1)
                continue
            if len(self.pool) >= config.spider.worker * 5:  # 每个队列最多5个进行中任务
                time.sleep(0.1)
                continue
            future = self.executor.submit(self.captcha.submit)
            self.pool.append(future)
            future.add_done_callback(
                self.remove_done_task
            )
            time.sleep(0.5)


if __name__ == '__main__':
    captcha_pool = CaptchaPool()
    captcha_pool.start_producer()