import base64
import io
import re
from collections import Counter
from typing import List
import cv2
import ddddocr
import numpy as np
from PIL import Image

from utils import find_background, bytes_image_to_cv2, image_to_cv2, OUT_IMAGE_ENTRIES, process_clear, show, \
    numpy_image_to_bytes, show_bytes_image


class Ocr:
    background_list = [image_to_cv2(entry) for entry in OUT_IMAGE_ENTRIES]

    def __init__(self):
        self.target_ocr = ddddocr.DdddOcr(det=True, show_ad=False)
        self.ocr = ddddocr.DdddOcr(show_ad=False)
        self.beta_ocr = ddddocr.DdddOcr(show_ad=False, beta=True)

    def recognize_text(self, image_byte,ocr_mode) -> str:
        """
        识别文字
        :param image_byte:
        :return:
        """
        text = ocr_mode.classification(image_byte,png_fix=True)
        if text is None:
            return ""
        return text

    def crop_image(self, image: Image, coordinate: List[int]) -> bytes:
        """
        剪切文字，并将Image转换为字节流
        :param image:
        :param coordinate:
        :return:
        """
        width, height = image.size
        expand_pixels = 5
        # 解构原始坐标
        left, upper, right, lower = coordinate

        # 扩大方框，注意不能超出图像边界
        new_left = max(0, left - expand_pixels)
        new_upper = max(0, upper - expand_pixels)
        new_right = min(width, right + expand_pixels)
        new_lower = min(height, lower + expand_pixels)

        # 扩大后的坐标
        expanded_coordinate = (new_left, new_upper, new_right, new_lower)

        # 进行裁剪
        cropped_image = image.crop(expanded_coordinate)
        # cropped_image.show()
        output = io.BytesIO()
        cropped_image.save(output, format='PNG')
        image_bytes = output.getvalue()
        return image_bytes

    def target_recognition(self, image_byte) -> List[List[int]]:
        """
        识别目标
        :return:
        """
        bboxes = self.target_ocr.detection(image_byte)
        return bboxes

    def target_point(self, image_byte):
        """
        识别目标的位置
        :param image_byte:
        :return:
        """
        return self.target_recognition(image_byte)

    def text_point(self, image_byte):
        """
        识别文字的位置
        :param image_byte:
        :return:
        """
        text_point = {}
        image_cv2 = bytes_image_to_cv2(image_byte)
        # show(image_cv2)
        background = find_background(image_cv2, self.background_list)
        # show(background)
        background = Image.fromarray(background)
        bboxes = self.target_ocr.detection(image_byte)
        image_stream = io.BytesIO(image_byte)
        image = Image.open(image_stream)
        for bbox in bboxes:
            cropped_image = self.crop_image(image, bbox)
            cropped_background_image = self.crop_image(background, bbox)

            clear_cropped_image = process_clear(cropped_image,cropped_background_image)
            clear_cropped_image = numpy_image_to_bytes(clear_cropped_image)

            for ocr_mode in (self.ocr,self.beta_ocr):
                # text = self.recognize_text(cropped_image,ocr_mode)
                for img in (cropped_image,clear_cropped_image):
                    text = self.recognize_text(img,ocr_mode)
                    text_point[text] = bbox
        return text_point