import json
import random
from urllib.parse import quote

import redis
from loguru import logger

from config import config


def build_redis_url(conf) -> str:
    """构建 Redis 连接 URL"""
    # 处理用户名密码编码（包含特殊字符的情况）
    username = quote(conf.redis.username) if conf.redis.username else None
    password = quote(conf.redis.password) if conf.redis.password else None

    # 组合认证信息
    auth_part = ""
    if username or password:
        auth = []
        if username:
            auth.append(username)
        if password:
            auth.append(password)
        auth_part = f"{':'.join(auth)}@"

    # 构建基础 URL
    base_url = f"redis://{auth_part}{conf.redis.host}:{conf.redis.port}"

    # 添加路径参数
    path_params = []
    if conf.redis.db is not None:
        path_params.append(f"/{conf.redis.db}")

    # 添加查询参数（如果需要额外参数）
    query_params = []

    # 组合完整 URL
    url = base_url
    if path_params:
        url += "".join(path_params)
    if query_params:
        url += "?" + "&".join(query_params)
    return url

def from_redis_setting(config):
    """
    redis://:yourpassword@localhost:6379/0
    redis://localhost:6379/0
    """
    redis_url = build_redis_url(config)
    return redis.StrictRedis.from_url(redis_url)


class RedisServer:
    redis_client = from_redis_setting(config)
    set_key = config.spider.redis_key + ":set"

    def push_queue(self,result):
        value = json.dumps(result)
        self.redis_client.setex(f"{config.spider.redis_key}:{result.get('token')}", config.spider.expire_second, value)



    def safe_zadd_with_size_limit(self, queue_name: str, redis_pool_len: int) -> bool:
        """带容量检查的安全添加"""
        keys = self.redis_client.keys(f"{config.spider.redis_key}:*")
        current_size = len(keys)
        if current_size >= redis_pool_len:
            logger.debug(f"队列 {queue_name} 已达容量限制 | 当前: {current_size} 限制: {redis_pool_len}")
            return True
        logger.info(f"[*]:当前队列总数{current_size}")
        return False

    def get_and_delete_random_key(self):
        """
        随机获取并删除一个匹配指定模式的 Redis 键

        Args:
            redis_client: Redis 客户端实例
            key_pattern: 要匹配的键模式，例如 "prefix:*"

        Returns:
            返回被删除键的值，如果没有匹配的键则返回 None
        """
        # 获取所有匹配模式的键
        keys = self.redis_client.keys(f"{config.spider.redis_key}:*")
        if not keys:
            return
        # 随机选择一个键
        random_key = random.choice(keys)
        # 获取键的值
        value = self.redis_client.get(random_key)
        # 删除键
        self.redis_client.delete(random_key)
        return json.loads(value.decode())

redis_server = RedisServer()


