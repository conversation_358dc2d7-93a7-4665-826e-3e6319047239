import cv2
import numpy as np
import onnxruntime

import utils


class Ocr:
    yolov11_session = onnxruntime.InferenceSession("/Users/<USER>/work/juming/beian-mps/ocr/yolov11.onnx")  # 加载 ONNX 模型
    siamese_session = onnxruntime.InferenceSession("/Users/<USER>/work/juming/beian-mps/ocr/siamese.onnx")  # 加载Siamese网络模型
    class_names = ["text"]  # 检测类别名称
    input_size = (512, 512)  # 输入图像的尺寸
    conf_threshold = 0.8  # 置信度阈值
    nms_threshold = 0.7  # 非极大值抑制阈值

    def detect_objects(self,background_image):
        height, width, _ = background_image.shape  # 获取图像尺寸
        length = max(self.input_size)  # 获取最长边长度
        if height > length or width > length:
            big_original_image = cv2.resize(background_image, (512, 192))
            height, width, _ = big_original_image.shape  # 获取图像尺寸
        image = np.zeros((length, length, 3), np.uint8)  # 创建方形黑色背景
        image[0:height, 0:width] = background_image  # 将原图填充到方形图像中
        scale = length / self.input_size[0]  # 计算缩放比例
        blob = cv2.dnn.blobFromImage(image, scalefactor=1 / 255.0, size=self.input_size, swapRB=True)
        input_name = self.yolov11_session.get_inputs()[0].name
        outputs = self.yolov11_session.run(None, {input_name: blob})  # 运行模型推理
        outputs = np.array([cv2.transpose(outputs[0][0])])  # 转置输出数据
        rows = outputs.shape[1]  # 获取检测框数量
        boxes, scores, class_ids = [], [], []
        # 解析检测结果
        for i in range(rows):
            class_scores = outputs[0][i][4:]  # 获取所有类别的得分
            _, max_score, _, (x, max_class_idx) = cv2.minMaxLoc(class_scores)  # 获取最大类别得分
            if max_score >= self.conf_threshold:  # 如果得分高于阈值，则保存检测框
                box = [
                    int(outputs[0][i][0] - (0.5 * outputs[0][i][2])),  # 左上角 x
                    int(outputs[0][i][1] - (0.5 * outputs[0][i][3])),  # 左上角 y
                    int(outputs[0][i][2]),  # 宽度
                    int(outputs[0][i][3]),  # 高度
                ]
                boxes.append(box)
                scores.append(max_score)
                class_ids.append(max_class_idx)

        # 进行非极大值抑制（NMS）
        result_indices = cv2.dnn.NMSBoxes(boxes, scores, self.conf_threshold, self.nms_threshold)
        boxes_list = []
        if len(result_indices) > 0:
            for i in result_indices.flatten():
                box = boxes[i]
                detection = {
                    "class_id": class_ids[i],
                    "class_name": self.class_names[class_ids[i]],
                    "confidence": scores[i],
                    "box": [
                        round(box[0] * scale),
                        round(box[1] * scale),
                        round(box[2] * scale),
                        round(box[3] * scale),
                    ],
                }
                boxes_list.append(detection)
        return boxes_list


    def siamese(self,background_image,small_image,box_list):
        """
        使用Siamese网络进行目标比对，判断是否为同一个物体
        :param box_list:
        :param small_image:
        :param background_image:
        :return: 比对结果列表，包含被判定为同一物体的目标框的左上角坐标 (x, y)
        """
        # 设置待检测区域在小图像上的 x 坐标位置，这些是预定义的关键区域
        positions = [165, 200, 231, 265]
        # 用于存储匹配成功的目标框坐标
        result_list = []
        # 遍历预定义的位置，提取小图像中的区域进行比对
        for x in positions:
            # 如果已找到4个匹配的目标，则提前结束循环
            if len(result_list) == 4:
                break

            # 截取小图像的指定区域（从坐标 (11, x) 开始，尺寸为 28x26）
            raw_image2 = small_image[11:11 + 28, x:x + 26]
            # 将图像从 BGR 转换为 RGB 格式（OpenCV 默认 BGR，而模型通常使用 RGB）
            img2 = cv2.cvtColor(raw_image2, cv2.COLOR_BGR2RGB)
            # 调整图像尺寸为 (105, 105)，适应 Siamese 网络的输入要求
            img2 = cv2.resize(img2, (105, 105))
            # 对小图像进行归一化处理，将像素值缩放到 [0,1] 范围，提升模型稳定性
            image_data_2 = np.array(img2) / 255.0
            # 调整图像的通道顺序，从 (H, W, C) 变为 (C, H, W)，符合深度学习框架的输入格式
            image_data_2 = np.transpose(image_data_2, (2, 0, 1))
            # 增加 batch 维度，变为 (1, 3, 105, 105)，以适应模型输入
            image_data_2 = np.expand_dims(image_data_2, axis=0).astype(np.float32)
            # 遍历检测到的目标框，与小图像进行比对
            for box in box_list:
                box = box["box"]
                # 提取大图像中的目标区域（根据检测框的坐标和尺寸裁剪）
                raw_image1 = background_image[box[1]:box[1] + box[3] + 2, box[0]:box[0] + box[2] + 2]
                # 将目标区域从 BGR 转换为 RGB 格式
                img1 = cv2.cvtColor(raw_image1, cv2.COLOR_BGR2RGB)
                # 调整图像大小为 (105, 105)，以匹配 Siamese 网络输入要求
                img1 = cv2.resize(img1, (105, 105))
                # 对目标区域进行归一化处理
                image_data_1 = np.array(img1) / 255.0
                # 调整通道顺序 (H, W, C) -> (C, H, W)
                image_data_1 = np.transpose(image_data_1, (2, 0, 1))
                # 增加 batch 维度，变为 (1, 3, 105, 105)
                image_data_1 = np.expand_dims(image_data_1, axis=0).astype(np.float32)
                # 准备输入数据，将小图和目标区域图像一起输入到 Siamese 网络
                inputs = {'input': image_data_1, "input.53": image_data_2}
                # 运行 Siamese 网络，进行图像相似度比对
                output = self.siamese_session.run(None, inputs)
                # 使用 Sigmoid 函数将网络输出转换为 0-1 之间的相似度分数
                output_sigmoid = 1 / (1 + np.exp(-output[0]))
                # 获取相似度分数（0 表示完全不同，1 表示完全相同）
                res = output_sigmoid[0][0]
                # 如果相似度分数大于阈值 0.7，则认为是同一物体，记录坐标
                if res >= 0.7:
                    result_list.append([box[0], box[1]])  # 记录检测框左上角坐标
                    break  # 跳出循环，避免重复记录同一目标
        # 返回最终匹配成功的检测框坐标列表
        return result_list


    def ocr(self,background_image,small_image):
        background_image = utils.bytes_image_to_cv2(background_image)
        small_image = utils.bytes_image_to_cv2(small_image)
        box_list = self.detect_objects(background_image)
        if not box_list:
            raise Exception("未检测到目标")
        result_list = self.siamese(background_image,small_image,box_list)
        return result_list